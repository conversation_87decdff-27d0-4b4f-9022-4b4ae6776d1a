"use client"

import { User<PERSON><PERSON><PERSON><PERSON>, useUser, useClerk } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { trpc } from '@/utils/trpc'
import { useState } from "react"
import Link from "next/link"
import {
  User,
  Shield,
  CreditCard,
  Mail,
  Link2,
  PlusCircle,
  ChevronLeft,
  UploadCloud,
  Globe,
  CheckCircle,
  Bot,
  Lightbulb,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import AuthenticatedNavbar from "@/components/authenticated-navbar"
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation"
import PricingSection from "@/components/pricing-section"
import PersonalitySelector from "@/components/ui/personality-selector"
import ModelSelector from "@/components/ui/model-selector"

type ProfileSection = "profile" | "security" | "billing"

export default function UserProfilePage() {
  const { user, isLoaded } = useUser()
  const { signOut } = useClerk()
  const { data: userProfile, isLoading: profileLoading, refetch } = trpc.user.getProfile.useQuery()
  const { data: usage } = trpc.user.getUsage.useQuery()
  const updateProfile = trpc.user.updateProfile.useMutation()
  
  const [activeSection, setActiveSection] = useState<ProfileSection>("profile")
  const [username, setUsername] = useState("")
  const [isEditingUsername, setIsEditingUsername] = useState(false)
  const [personalityDialogOpen, setPersonalityDialogOpen] = useState(false)
  const [modelDialogOpen, setModelDialogOpen] = useState(false)

  if (!isLoaded || profileLoading) {
    return (
      <div className="min-h-screen bg-app-background flex items-center justify-center">
        <ShardLoadingAnimation size={80} />
      </div>
    )
  }
  
  if (!user) {
    redirect('/sign-in')
  }

  const handleUpdateUsername = async () => {
    try {
      await updateProfile.mutateAsync({ name: username })
      await refetch()
      setIsEditingUsername(false)
    } catch (error) {
      console.error('Failed to update username:', error)
    }
  }

  const renderSectionContent = () => {
    switch (activeSection) {
      case "profile":
        return <ProfileSectionContent />
      case "security":
        return <SecuritySectionContent />
      case "billing":
        return <PricingSection />
      default:
        return null
    }
  }

  const ProfileSectionContent = () => (
    <div className="space-y-6">
      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader>
          <CardTitle className="text-app-headline">Profile</CardTitle>
          <CardDescription className="text-app-headline/70">
            This is how others will see you on the site.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <Avatar className="h-20 w-20 border-2 border-app-main">
              <AvatarImage src={user.imageUrl} alt={user.firstName || 'Profile'} />
              <AvatarFallback className="bg-app-main text-app-secondary text-2xl">
                {user.firstName?.charAt(0) || user.emailAddresses[0]?.emailAddress.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <h3 className="text-xl font-semibold text-app-headline">
                {userProfile?.user.name || user.firstName + ' ' + (user.lastName || '') || 'User'}
              </h3>
              <Button
                variant="outline"
                size="sm"
                className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary"
              >
                <UploadCloud className="w-4 h-4 mr-2" /> Change photo
              </Button>
            </div>
          </div>
          <Separator className="bg-app-stroke/20" />
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-app-headline mb-1">
              Display Name
            </label>
            {isEditingUsername ? (
              <div className="flex items-center space-x-2">
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="bg-app-background border-app-stroke text-app-headline placeholder:text-app-headline/60"
                />
                <Button
                  onClick={handleUpdateUsername}
                  disabled={updateProfile.isPending}
                  className="bg-app-main text-app-secondary hover:bg-app-highlight"
                >
                  {updateProfile.isPending ? 'Saving...' : 'Save'}
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => setIsEditingUsername(false)}
                  className="text-app-headline hover:bg-app-main/20"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <div className="flex items-center justify-between p-2 rounded-md hover:bg-app-background">
                <span className="text-app-headline">
                  {userProfile?.user.name || user.firstName + ' ' + (user.lastName || '') || 'Not set'}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setUsername(userProfile?.user.name || user.firstName + ' ' + (user.lastName || '') || '')
                    setIsEditingUsername(true)
                  }}
                  className="text-app-main hover:text-app-highlight"
                >
                  Edit
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader>
          <CardTitle className="text-app-headline">Contact Information</CardTitle>
          <CardDescription className="text-app-headline/70">
            Manage your email addresses and connected accounts.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Email Addresses */}
          <div>
            <h4 className="font-medium text-app-headline mb-2">Email addresses</h4>
            <div className="space-y-2">
              {user.emailAddresses.map((emailItem, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded-md hover:bg-app-background">
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-app-main" />
                    <div>
                      <span className="text-app-headline">{emailItem.emailAddress}</span>
                      <div className="flex items-center space-x-2">
                        {user.primaryEmailAddressId === emailItem.id && (
                          <Badge className="bg-app-main text-app-secondary">Primary</Badge>
                        )}
                        <span className="flex items-center text-xs text-green-600">
                          <CheckCircle className="w-3 h-3 mr-1" /> Verified
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator className="bg-app-stroke/20" />

          {/* Connected Accounts */}
          <div>
            <h4 className="font-medium text-app-headline mb-2">Connected accounts</h4>
            <div className="space-y-2">
              {user.externalAccounts.map((account, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded-md hover:bg-app-background">
                  <div className="flex items-center space-x-3">
                    <Globe className="w-5 h-5 text-blue-500" />
                    <div>
                      <span className="font-medium text-app-headline capitalize">{account.provider}</span>
                      <p className="text-xs text-app-headline/70">{account.emailAddress}</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" className="text-app-highlight hover:text-app-main">
                    Disconnect
                  </Button>
                </div>
              ))}
            </div>
            <Button
              variant="outline"
              className="w-full mt-4 border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary"
            >
              <Link2 className="w-4 h-4 mr-2" /> Connect another account
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* AI Settings */}
      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-app-headline text-lg sm:text-xl">AI Settings</CardTitle>
          <CardDescription className="text-app-headline/70 text-sm sm:text-base leading-relaxed">
            Configure your AI personality and model preferences for generating replies.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
            <Button
              variant="outline"
              onClick={() => setPersonalityDialogOpen(true)}
              className="border-app-stroke text-app-headline hover:bg-app-main/10 hover:border-app-main hover:text-app-main transition-all duration-200 h-auto p-3 sm:p-4 flex flex-col items-start space-y-2 min-h-[100px] sm:min-h-[120px] w-full"
            >
              <div className="flex items-center space-x-2 w-full">
                <User className="w-4 h-4 sm:w-5 sm:h-5 text-app-main flex-shrink-0" />
                <span className="font-medium text-sm sm:text-base">AI Personality</span>
              </div>
              <p className="text-xs sm:text-sm text-app-headline/70 text-left leading-relaxed w-full">
                Choose how your AI responds to tweets with different personality styles.
              </p>
            </Button>

            <Button
              variant="outline"
              onClick={() => setModelDialogOpen(true)}
              className="border-app-stroke text-app-headline hover:bg-app-main/10 hover:border-app-main hover:text-app-main transition-all duration-200 h-auto p-3 sm:p-4 flex flex-col items-start space-y-2 min-h-[100px] sm:min-h-[120px] w-full"
            >
              <div className="flex items-center space-x-2 w-full">
                <Bot className="w-4 h-4 sm:w-5 sm:h-5 text-app-main flex-shrink-0" />
                <span className="font-medium text-sm sm:text-base">AI Model</span>
              </div>
              <p className="text-xs sm:text-sm text-app-headline/70 text-left leading-relaxed w-full">
                Select the AI model to use for generating responses and analysis.
              </p>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Subscription Details */}
      {userProfile?.plan && (
        <Card className="bg-app-card border-app-stroke shadow-sm">
          <CardHeader>
            <CardTitle className="text-app-headline">Subscription Plan</CardTitle>
            <CardDescription className="text-app-headline/70">
              Your current plan and usage details.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-app-main/10 border border-app-main/20 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-app-headline text-lg">
                    {userProfile.plan.displayName}
                  </h3>
                  <p className="text-app-headline opacity-70">
                    ${userProfile.plan.price}/month
                  </p>
                  <p className="text-sm text-app-headline opacity-70 mt-1">
                    {userProfile.plan.description}
                  </p>
                </div>
                <Button className="bg-app-main text-app-secondary hover:bg-app-highlight">
                  Upgrade Plan
                </Button>
              </div>
            </div>

            {/* Usage Stats */}
            {usage && (
              <div className="space-y-3">
                <h4 className="font-medium text-app-headline">Current Usage</h4>
                {usage.map((item: any) => (
                  <div key={item.feature} className="flex items-center justify-between p-3 bg-app-background rounded-md">
                    <div>
                      <span className="text-app-headline font-medium">
                        {item.feature.replace('_', ' ').toLowerCase().replace(/\b\w/g, (l: string) => l.toUpperCase())}
                      </span>
                      <p className="text-sm text-app-headline opacity-70">
                        {item.currentUsage} / {item.limit === -1 ? '∞' : item.limit}
                      </p>
                    </div>
                    {item.limit !== -1 && (
                      <div className="w-20 h-2 bg-app-stroke rounded-full">
                        <div 
                          className="h-2 bg-app-main rounded-full" 
                          style={{ width: `${Math.min((item.currentUsage / item.limit) * 100, 100)}%` }}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )

  const SecuritySectionContent = () => (
    <div className="space-y-6">
      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader>
          <CardTitle className="text-app-headline">Account Security</CardTitle>
          <CardDescription className="text-app-headline/70">
            Manage your password, two-factor authentication, and security settings.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="clerk-profile-container">
            <UserProfile 
              appearance={{
                elements: {
                  card: 'bg-transparent shadow-none border-0',
                  navbar: 'hidden',
                  pageScrollBox: 'p-0',
                  page: 'bg-transparent',
                  rootBox: 'w-full',
                  profileSection: 'bg-app-background border border-app-stroke rounded-lg p-4 mb-4',
                  profileSectionTitle: 'text-app-headline',
                  profileSectionContent: 'text-app-headline',
                  formButtonPrimary: 'bg-app-main hover:bg-app-highlight text-app-secondary',
                  formFieldLabel: 'text-app-headline',
                  formFieldInput: 'border-app-stroke focus:border-app-main bg-app-background',
                  identityPreview: 'bg-app-background border border-app-stroke',
                  accordionTriggerButton: 'text-app-headline hover:bg-app-background',
                },
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )



  return (
    <div className="min-h-screen p-4 md:p-8 font-sans bg-app-background text-app-headline">
      <AuthenticatedNavbar currentPage="profile" />
      
      <header className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-app-headline">Settings</h1>
          <p className="text-app-headline opacity-70">Manage your account and preferences</p>
        </div>
        <Button
          variant="outline"
          asChild
          className="border-app-stroke bg-app-card hover:bg-app-main hover:text-app-secondary text-app-headline"
        >
          <Link href="/dashboard">
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Link>
        </Button>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Left Sidebar Navigation */}
        <aside className="lg:col-span-1">
          <nav className="sticky top-8 bg-app-card p-4 rounded-lg border border-app-stroke shadow-md space-y-1">
            <Button
              variant={activeSection === "profile" ? "secondary" : "ghost"}
              onClick={() => setActiveSection("profile")}
              className={`w-full justify-start text-base p-3 ${activeSection === "profile" ? "bg-app-main text-app-secondary hover:bg-app-highlight" : "text-app-headline hover:bg-app-main/20"}`}
            >
              <User className="w-5 h-5 mr-3" /> Profile
            </Button>
            <Button
              variant={activeSection === "security" ? "secondary" : "ghost"}
              onClick={() => setActiveSection("security")}
              className={`w-full justify-start text-base p-3 ${activeSection === "security" ? "bg-app-main text-app-secondary hover:bg-app-highlight" : "text-app-headline hover:bg-app-main/20"}`}
            >
              <Shield className="w-5 h-5 mr-3" /> Security
            </Button>
            <Button
              variant={activeSection === "billing" ? "secondary" : "ghost"}
              onClick={() => setActiveSection("billing")}
              className={`w-full justify-start text-base p-3 ${activeSection === "billing" ? "bg-app-main text-app-secondary hover:bg-app-highlight" : "text-app-headline hover:bg-app-main/20"}`}
            >
              <CreditCard className="w-5 h-5 mr-3" /> Billing
            </Button>
          </nav>
        </aside>

        {/* Main Content Area */}
        <main className="lg:col-span-3">{renderSectionContent()}</main>
      </div>

      {/* AI Settings Dialogs */}
      <PersonalitySelector
        isOpen={personalityDialogOpen}
        onClose={() => setPersonalityDialogOpen(false)}
      />

      <ModelSelector
        isOpen={modelDialogOpen}
        onClose={() => setModelDialogOpen(false)}
      />
    </div>
  )
}